"""
Mob Grief Protection Plugin for Endstone
Prevents mobs from destroying blocks while keeping damage mechanics intact
"""
from endstone.plugin import Plugin
from endstone.event import event_handler, ActorExplodeEvent, EventPriority
from endstone.command import Command, CommandSender
from endstone import ColorFormat
from pathlib import Path
import json


class MobGriefProtection(Plugin):
    """Main plugin class for Mob Grief Protection"""

    api_version = "0.10"

    def __init__(self):
        super().__init__()
        self.config = {
            "creeper_block_damage": False,
            "wither_block_damage": False,
            "enderman_block_pickup": False,
            "creeper_player_damage": True,
            "wither_player_damage": True
        }
        self.config_file: Path | None = None

    def on_load(self) -> None:
        """Called when the plugin is loaded"""
        self.logger.info("Loading Mob Grief Protection plugin...")
        self.load_config()

    def on_enable(self) -> None:
        """Called when the plugin is enabled"""
        self.logger.info("Mob Grief Protection plugin enabled!")
        self.logger.info(f"Creeper block damage: {self.config['creeper_block_damage']}")
        self.logger.info(f"Wither block damage: {self.config['wither_block_damage']}")
        self.logger.info(f"Enderman block pickup: {self.config['enderman_block_pickup']}")

        # Register event listeners
        self.server.plugin_manager.register_events(self, self)

        # Register commands
        self.register_commands()

    def on_disable(self) -> None:
        """Called when the plugin is disabled"""
        self.logger.info("Mob Grief Protection plugin disabled!")
        self.save_config()

    def load_config(self) -> None:
        """Load configuration from file"""
        # Create data folder if it doesn't exist
        if not self.data_folder.exists():
            self.data_folder.mkdir(parents=True, exist_ok=True)

        self.config_file = self.data_folder / "config.json"

        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                self.logger.info("Configuration loaded successfully")
            except Exception as e:
                self.logger.error(f"Failed to load config: {e}")
                self.logger.info("Using default configuration")
        else:
            self.save_config()
            self.logger.info("Created default configuration file")

    def save_config(self) -> None:
        """Save configuration to file"""
        try:
            if self.config_file:
                with open(self.config_file, 'w') as f:
                    json.dump(self.config, f, indent=4)
                self.logger.info("Configuration saved successfully")
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")

    def register_commands(self) -> None:
        """Register plugin commands"""
        cmd = self.get_command("mobgrief")
        if cmd:
            cmd.executor = self.on_command

    def on_command(self, sender: CommandSender, command: Command, args: list[str]) -> bool:
        """Handle plugin commands"""
        if command.name != "mobgrief":
            return False

        if len(args) == 0:
            self.send_help(sender)
            return True

        subcommand = args[0].lower()

        if subcommand == "status":
            self.show_status(sender)
            return True

        elif subcommand == "toggle":
            if len(args) < 2:
                sender.send_message(f"{ColorFormat.RED}Usage: /mobgrief toggle <setting>")
                sender.send_message(f"{ColorFormat.YELLOW}Settings: creeper, wither, enderman")
                return True

            setting = args[1].lower()
            return self.toggle_setting(sender, setting)

        elif subcommand == "reload":
            if not sender.has_permission("mobgrief.admin"):
                sender.send_message(f"{ColorFormat.RED}You don't have permission to use this command!")
                return True

            self.load_config()
            sender.send_message(f"{ColorFormat.GREEN}Configuration reloaded!")
            return True

        else:
            self.send_help(sender)
            return True

    def send_help(self, sender: CommandSender) -> None:
        """Send help message to command sender"""
        sender.send_message(f"{ColorFormat.GOLD}===== Mob Grief Protection =====")
        sender.send_message(f"{ColorFormat.YELLOW}/mobgrief status {ColorFormat.WHITE}- Show current settings")
        sender.send_message(f"{ColorFormat.YELLOW}/mobgrief toggle <setting> {ColorFormat.WHITE}- Toggle a setting")
        sender.send_message(f"{ColorFormat.YELLOW}/mobgrief reload {ColorFormat.WHITE}- Reload configuration")
        sender.send_message(f"{ColorFormat.GRAY}Settings: creeper, wither, enderman")

    def show_status(self, sender: CommandSender) -> None:
        """Show current protection status"""
        sender.send_message(f"{ColorFormat.GOLD}===== Mob Grief Protection Status =====")
        sender.send_message(
            f"{ColorFormat.YELLOW}Creeper Block Damage: "
            f"{self.format_status(self.config['creeper_block_damage'])}"
        )
        sender.send_message(
            f"{ColorFormat.YELLOW}Wither Block Damage: "
            f"{self.format_status(self.config['wither_block_damage'])}"
        )
        sender.send_message(
            f"{ColorFormat.YELLOW}Enderman Block Pickup: "
            f"{self.format_status(self.config['enderman_block_pickup'])}"
        )

    def format_status(self, enabled: bool) -> str:
        """Format status for display"""
        if enabled:
            return f"{ColorFormat.GREEN}Enabled"
        return f"{ColorFormat.RED}Disabled"

    def toggle_setting(self, sender: CommandSender, setting: str) -> bool:
        """Toggle a protection setting"""
        if not sender.has_permission("mobgrief.admin"):
            sender.send_message(f"{ColorFormat.RED}You don't have permission to use this command!")
            return True

        if setting == "creeper":
            self.config['creeper_block_damage'] = not self.config['creeper_block_damage']
            sender.send_message(
                f"{ColorFormat.GREEN}Creeper block damage is now "
                f"{self.format_status(self.config['creeper_block_damage'])}"
            )
            self.save_config()
            return True

        elif setting == "wither":
            self.config['wither_block_damage'] = not self.config['wither_block_damage']
            sender.send_message(
                f"{ColorFormat.GREEN}Wither block damage is now "
                f"{self.format_status(self.config['wither_block_damage'])}"
            )
            self.save_config()
            return True

        elif setting == "enderman":
            self.config['enderman_block_pickup'] = not self.config['enderman_block_pickup']
            sender.send_message(
                f"{ColorFormat.GREEN}Enderman block pickup is now "
                f"{self.format_status(self.config['enderman_block_pickup'])}"
            )
            self.save_config()
            return True

        else:
            sender.send_message(f"{ColorFormat.RED}Unknown setting: {setting}")
            sender.send_message(f"{ColorFormat.YELLOW}Available settings: creeper, wither, enderman")
            return False

    @event_handler(priority=EventPriority.HIGH)
    def on_actor_explode(self, event: ActorExplodeEvent) -> None:
        """Handle explosion events"""
        actor = event.actor

        if actor is None:
            return

        actor_type = actor.type.lower()

        # Handle creeper explosions
        if "creeper" in actor_type:
            if not self.config['creeper_block_damage']:
                # Clear the block list to prevent block damage
                event.blocks.clear()
                self.logger.debug(f"Prevented creeper explosion block damage at {event.location}")

        # Handle wither explosions (both wither entity and wither skull projectile)
        elif "wither" in actor_type:
            if not self.config['wither_block_damage']:
                # Clear the block list to prevent block damage
                event.blocks.clear()
                self.logger.debug(f"Prevented wither explosion block damage at {event.location}")